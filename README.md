# 图像标注工具

基于原始的 `check_imglab.py` 脚本，新开发了两个版本的图像标注工具，用于加速人工标注过程。

## 功能说明

### 原始功能 (check_imglab.py)
- 逐个打开子文件夹中的jpg图片
- 等待用户输入标签
- 根据标签移动或删除单个文件

### 新功能 (Check_imgs.py 和 Check_imgs_Win.py)
- 自动识别**样本文件夹**（包含1个pkl、1个avi、3个jpg的文件夹）
- 将样本文件夹中的3个jpg文件按照文件名末尾数字**纵向拼接**
- **🆕 骨骼图片拼接功能**：自动从pkl文件生成第4张骨骼图片，拼接在最下方
- 显示拼接后的图像供用户查看
- 等待用户输入标签，将**整个样本文件夹**移动到对应的标签目录
- 支持Ubuntu和Windows系统

### Web版本功能 (Check_imgs_Web.py) - 需求2实现
- **网页可视化界面** - 在本地浏览器中操作，无需频繁打开关闭图片查看器
- **批量图片展示** - 同时显示多个样本的拼接图像，提高标注效率
- **🆕 骨骼图片拼接** - 每个样本显示4张图片（3张原图+1张骨骼图）
- **多选批量标注** - 可选择多个样本进行批量处理，一次操作标注多个样本
- **实时状态更新** - 显示样本的处理状态（未处理/已处理/已删除）
- **跨平台兼容** - 支持Windows 10、Linux、macOS，通过浏览器访问

### 性能优化功能 (v2.0新增)
- **智能样本隐藏** - 已处理和已删除的样本自动从界面隐藏，保持界面清爽
- **懒加载机制** - 快速启动，后台多线程生成拼接图片，大幅提升启动速度
- **多线程处理** - 使用4线程并行处理，显著减少大量样本的等待时间
- **实时进度指示** - 可视化进度条显示图片生成进度，用户体验更好
- **增强视频播放** - 支持多种Linux播放器，详细日志，解决Ubuntu播放问题
- **动态界面更新** - 图片生成完成后自动更新界面，无需手动刷新

### 撤销功能 (v2.1新增)
- **Ctrl+Z 撤销** - 支持撤销最近一次的标注操作（不包括删除操作）
- **智能选中恢复** - 撤销后样本自动保持选中状态，便于重新标注
- **文件系统还原** - 样本文件夹从标签目录自动移回原始位置
- **操作历史记录** - 自动记录标注操作的详细信息，支持精确撤销
- **日志同步清理** - 撤销时自动从日志文件中移除相应记录
- **跨平台快捷键** - 支持 Windows/Linux 的 Ctrl+Z 和 macOS 的 Cmd+Z


### 前端UI优化（v2.2新增）
- 状态提示信息（如“成功处理 N 个样本”“所有图片生成完成”“正在重新扫描样本…/刷新完成”“撤销/播放视频 等提示”）由页面上端调整为页面右上角以浮层 toast 形式显示。
- 仅调整显示位置，提示内容与出现时机保持不变；键盘快捷键与交互逻辑不变。
- 响应式设计：小屏设备下自动收敛最大宽度；采用 pointer-events 控制，避免遮挡页面中的操作元素。
- 实现位置：`Check_imgs_Web.py` 的内联 CSS 新增 `#message-container` 的固定定位样式（top/right/z-index/响应式 max-width 等），不改动现有 `showMessage()` 触发逻辑。
- 影响范围：仅 Web 版本（`Check_imgs_Web.py`）。CLI/桌面版本不受影响。

### 右键打开样本文件夹（v2.3新增）
- 在 Web 页面中，**右键单击样本卡片**，会自动用系统默认的文件夹管理器打开该样本所在文件夹（包含 1 个 `.avi`、1 个 `.pkl` 和 3 个 `.jpg`）。
- 跨平台支持：
  - **Windows**: 使用系统 `startfile`
  - **macOS**: 使用 `open` 命令
  - **Linux**: 使用 `xdg-open`（需系统已配置默认文件管理器）
- 使用方式：运行 Web 版后，加载出样本列表，在任意样本卡片上点击鼠标右键即可。

### 界面交互优化（v2.4新增）
- **标签按钮布局优化** - 每个样本下方的标签按钮重新布局：红色删除按钮（×）居右显示，数字按钮（0-9）和字母按钮（b）居左显示，提升操作的直观性和美观度
- **悬停放大效果增强** - 样本图像的鼠标悬停放大效果从1.02倍增强到1.1倍，放大效果更加明显，便于查看图像细节
- **统一交互体验** - 所有小按钮（标签按钮）保持1.1倍的悬停放大效果，与图像悬停效果保持一致，整体交互体验更加协调
- **视觉层次优化** - 通过布局调整和悬停效果的统一，提升了界面的视觉层次感和操作便利性

### 🆕 骨骼图片拼接功能（v2.5新增）
- **自动骨骼提取** - 从pkl文件的`pred_skpts`数据中提取17个COCO关键点
- **彩色轨迹显示** - 每4帧使用相同颜色，整体从深蓝到浅蓝渐变，清晰显示运动轨迹
- **智能尺寸适配** - 骨骼图片自动适配原图尺寸，保持视觉一致性
- **完整拼接展示** - 在原有3张图片基础上，底部新增第4张骨骼图片
- **跨版本支持** - Linux版、Windows版、Web版均支持骨骼图片拼接
- **错误处理机制** - pkl文件异常时自动降级为3张图片拼接，确保功能稳定性


## 文件说明

- `Check_imgs.py` - Ubuntu/Linux版本
- `Check_imgs_Win.py` - Windows版本
- `Check_imgs_Web.py` - Web版本（需求2实现）
- `demo_web.py` - Web版演示脚本
- `requirements.txt` - Python依赖包
- `check_imglab.py` - 原始脚本（不要修改）
- `readMoss.txt` - 需求文档

## 环境要求

### Python依赖
```bash
pip install pynput Pillow Flask
```

### 系统要求

**Ubuntu/Linux:**
- 需要图形界面环境
- 需要 `eog` (Eye of GNOME) 图片查看器
- 需要 X11 环境支持键盘监听

**Windows:**
- Windows 7/10/11
- 系统自带图片查看器或其他默认图片程序

## 撤销功能使用说明

### 基本用法
1. **标注样本**: 选择样本并使用快捷键或按钮进行标注
2. **触发撤销**: 按 `Ctrl+Z` (Windows/Linux) 或 `Cmd+Z` (macOS)
3. **或点击按钮**: 使用界面顶部出现的撤销按钮
4. **样本恢复**: 撤销的样本会自动恢复为选中状态，便于重新标注

### 撤销特性
- ✅ **只撤销移动操作**: 标签 0-9、b 的标注可以撤销
- ❌ **删除操作不可撤销**: 使用 'd' 键删除的样本无法恢复
- 🎯 **智能选中**: 撤销后样本自动保持选中，便于重新操作
- 📁 **文件还原**: 样本从标签文件夹移回原位置
- 📝 **日志清理**: 自动从日志文件中移除撤销的记录
- ⏮️ **单次操作**: 只能撤销最近一次的标注操作

### 注意事项
- 撤销功能只保留最近一次操作历史
- 删除操作出于安全考虑不可撤销
- 撤销后建议立即重新标注，避免误操作
- 大量样本标注前建议先少量测试

## 使用方法

### 1. Ubuntu/Linux版本

```bash
# 安装依赖
pip install -r requirements.txt

# 运行脚本
python Check_imgs.py
```

### 2. Windows版本

```bash
# 安装依赖
pip install -r requirements.txt

# 运行脚本
python Check_imgs_Win.py
```

### 3. Web版本（推荐）

```bash
# 安装依赖
pip install -r requirements.txt

# 快速演示
python demo_web.py

# 或直接运行Web版本
python Check_imgs_Web.py
```

**Web版本优势：**
- 无需配置图片查看器，通过浏览器操作
- 支持批量选择和标注，效率更高
- 实时显示处理状态，操作直观
- 跨平台兼容性更好

**Web版本操作流程：**
1. 运行脚本后自动打开浏览器（快速启动，无需等待）
2. 观察进度条显示图片生成进度
3. 页面显示所有未处理样本的拼接图像
4. **单击**样本卡片选择样本（支持多选）
5. **双击**样本卡片播放对应的视频文件
6. **右键**样本卡片打开样本所在文件夹
7. **鼠标悬停**样本图像查看放大效果（1.1倍放大）
8. 点击样本下方的标签按钮或使用键盘快捷键（0-9、b、d）进行批量标注
   - 数字按钮（0-9）和字母按钮（b）位于左侧
   - 红色删除按钮（×）位于右侧
   - 所有按钮支持悬停放大效果，便于精确点击
9. 已标注样本自动隐藏，实时查看处理状态
10. **标注错误时按 Ctrl+Z 撤销**，撤销的样本保持选中状态

**性能基准参考：**
- 20个样本以下：几秒内完成所有处理
- 50-200个样本：启动后数十秒完成图片生成
- 200-1000个样本：几分钟完成，期间可正常使用
- 1000-3000个样本：数分钟到十几分钟，显著优于原版本

### 4. 配置路径

在脚本中修改以下变量为你的实际路径：

**Ubuntu版本:**
```python
root_folder_path = "/your/path/to/samples"
```

**Windows版本:**
```python
root_folder_path = r"C:\Users\<USER>\Desktop\Check_Methods\samples"
```

**Web版本:**
Web版本启动时会提示输入样本目录路径，或使用默认路径。也可以直接修改 `Check_imgs_Web.py` 中的默认路径。

## 样本文件夹结构

每个**样本文件夹**应包含：
- 1个 `.pkl` 文件（包含骨骼关键点数据，用于生成第4张骨骼图片）
- 1个 `.avi` 文件
- 3个 `.jpg` 文件（文件名末尾包含数字，如：`xxx-1096.jpg`、`xxx-1128.jpg`、`xxx-1151.jpg`）

示例文件夹结构：
```
samples/
├── scl0/
│   └── 2024_12_25_8_56_5_192.168.2.210_1_3_A__len28_loop1255_1282_25/
│       ├── 2024_12_25_8_56_5_192.168.2.210_1_3_A__len28_loop1255_1282_25-1255.jpg
│       ├── 2024_12_25_8_56_5_192.168.2.210_1_3_A__len28_loop1255_1282_25-1269.jpg
│       ├── 2024_12_25_8_56_5_192.168.2.210_1_3_A__len28_loop1255_1282_25-1282.jpg
│       ├── 2024_12_25_8_56_5_192.168.2.210_1_3_A__len28_loop1255_1282_25.avi
│       └── 2024_12_25_8_56_5_192.168.2.210_1_3_A__len28_loop1255_1282_25.pkl
└── scl2/
    └── 2024_12_13_14_6_17_192.168.2.210_1_3_A__len35_loop1096_1151_17/
        ├── 2024_12_13_14_6_17_192.168.2.210_1_3_A__len35_loop1096_1151_17-1096.jpg
        ├── 2024_12_13_14_6_17_192.168.2.210_1_3_A__len35_loop1096_1151_17-1128.jpg
        ├── 2024_12_13_14_6_17_192.168.2.210_1_3_A__len35_loop1096_1151_17-1151.jpg
        ├── 2024_12_13_14_6_17_192.168.2.210_1_3_A__len35_loop1096_1151_17.avi
        └── 2024_12_13_14_6_17_192.168.2.210_1_3_A__len35_loop1096_1151_17.pkl
```

## 操作流程

1. **启动脚本** - 运行对应系统版本的脚本
2. **自动识别** - 脚本会递归搜索所有样本文件夹
3. **图片拼接** - 自动将3个jpg图片按数字顺序纵向拼接
4. **显示图片** - 在图片查看器中显示拼接后的图像
5. **输入标签** - 按键盘数字或字母键进行分类：
   - `0`, `1`, `2`, `3`, `4`, `5`, `6`, `7`, `8`, `9`, `b` - 标签分类（移动样本文件夹到对应目录）
   - `d` - 删除整个样本文件夹
6. **自动处理** - 根据输入标签移动或删除样本文件夹
7. **继续处理** - 自动处理下一个样本文件夹

## 输出结果

- **标签目录**: `{原目录名}_labels/`
- **分类文件夹**: `{原目录名}_labels/0/`, `{原目录名}_labels/1/`, 等
- **日志文件**: `{原目录名}_labels/sample_labels.txt`

## 图片拼接说明

脚本会按照文件名末尾的数字对jpg文件进行排序，然后纵向拼接：
- 数字最小的图片在最上方
- 数字最大的图片在最下方
- 如果图片宽度不一致，会居中对齐

例如：
- `xxx-1096.jpg` (最上)
- `xxx-1128.jpg` (中间)
- `xxx-1151.jpg` (最下)

## 注意事项

1. **不要修改** `check_imglab.py` 文件
2. 确保样本文件夹符合格式要求（1个pkl + 1个avi + 3个jpg）
3. 脚本会自动创建标签目录和日志文件
4. 按 `Ctrl+C` 可以中断脚本运行
5. Windows版本可能需要手动关闭图片查看器窗口
6. 建议在处理重要数据前先备份

## 故障排除

### Ubuntu/Linux
- 如果图片无法显示，确保安装了 `eog` 图片查看器
- 如果键盘监听不工作，确保在图形界面环境下运行

### Windows
- 如果图片无法打开，确保系统有默认的图片查看程序
- 如果出现权限问题，尝试以管理员身份运行

## 测试

脚本已通过测试：
- ✅ 样本文件夹检测功能
- ✅ 文件名数字提取和排序
- ✅ 图片拼接功能
- ✅ 基本的文件操作功能

## 作者

Created by @Moss 2025.03.28
